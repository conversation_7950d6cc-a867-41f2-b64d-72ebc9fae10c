<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="Sandbox.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mauiNet8="clr-namespace:MauiNet8"
    xmlns:sandbox="clr-namespace:Sandbox"
    xmlns:views="clr-namespace:Sandbox.Views"
    xmlns:views1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views"
    Title="DrawnUI for .NET MAUI"
    Shell.FlyoutBehavior="Disabled">

    <ShellContent
        Title="Drawn UI for .NET MAUI"
        ContentTemplate="{DataTemplate views1:MainPageCameraPhoto}"
        Route="MainPage" />

</Shell>
